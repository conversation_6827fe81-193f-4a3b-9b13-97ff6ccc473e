import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../../../api/orders_api.dart';
import '../models/wholesaler_models.dart';

/// Order status badge component
class OrderStatusBadge extends StatelessWidget {
  final OrderStatus status;
  final double? fontSize;
  final EdgeInsets? padding;

  const OrderStatusBadge({
    super.key,
    required this.status,
    this.fontSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getStatusColor().withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: _getStatusColor(),
          fontWeight: FontWeight.bold,
          fontSize: fontSize ?? 12,
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case OrderStatus.pending:
        return const Color(0xFF4044AA);
      case OrderStatus.processing:
        return const Color(0xFF41A954);
      case OrderStatus.shipped:
        return const Color(0xFFE19603);
      case OrderStatus.delivered:
        return const Color(0xFF41AA55);
      case OrderStatus.cancelled:
        return const Color(0xFFFF1F1F);
    }
  }
}

/// Order status icon component
class OrderStatusIcon extends StatelessWidget {
  final OrderStatus status;
  final double size;

  const OrderStatusIcon({
    super.key,
    required this.status,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: _getStatusColor(),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getStatusIcon(),
        color: Colors.white,
        size: size * 0.6,
      ),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case OrderStatus.pending:
        return const Color(0xFF4044AA);
      case OrderStatus.processing:
        return const Color(0xFF41A954);
      case OrderStatus.shipped:
        return const Color(0xFFE19603);
      case OrderStatus.delivered:
        return const Color(0xFF41AA55);
      case OrderStatus.cancelled:
        return const Color(0xFFFF1F1F);
    }
  }

  IconData _getStatusIcon() {
    switch (status) {
      case OrderStatus.pending:
        return Icons.schedule;
      case OrderStatus.processing:
        return Icons.hourglass_empty;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }
}

/// Order status timeline component
class OrderStatusTimeline extends StatelessWidget {
  final WholesalerOrder order;

  const OrderStatusTimeline({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    final statuses = _getStatusList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تتبع حالة الطلب',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        ...statuses.asMap().entries.map((entry) {
          final index = entry.key;
          final statusInfo = entry.value;
          final isLast = index == statuses.length - 1;

          return _TimelineItem(
            status: statusInfo.status,
            isActive: statusInfo.isActive,
            isCompleted: statusInfo.isCompleted,
            isLast: isLast,
            date: statusInfo.date,
            time: statusInfo.time,
          );
        }),
      ],
    );
  }

  List<_StatusInfo> _getStatusList() {
    final List<_StatusInfo> statuses = [];

    // Add normal flow statuses
    final normalStatuses = [
      OrderStatus.pending,
      OrderStatus.processing,
      OrderStatus.shipped,
      OrderStatus.delivered,
    ];

    for (final status in normalStatuses) {
      final isCompleted = _isStatusCompleted(status);
      final isActive =
          status == order.status && order.status != OrderStatus.cancelled;

      statuses.add(_StatusInfo(
        status: status,
        isActive: isActive,
        isCompleted: isCompleted,
        date: _getStatusDate(status),
        time: _getStatusTime(status),
      ));

      // If current status is reached and not cancelled, stop here
      if (status == order.status && order.status != OrderStatus.cancelled) {
        break;
      }
    }

    // Add cancelled status if order is cancelled
    if (order.status == OrderStatus.cancelled) {
      statuses.add(_StatusInfo(
        status: OrderStatus.cancelled,
        isActive: true,
        isCompleted: true,
        date: order.formattedStatusDate,
        time: _formatTime(order.statusUpdatedAt),
      ));
    }

    return statuses;
  }

  bool _isStatusCompleted(OrderStatus status) {
    final statusOrder = {
      OrderStatus.pending: 0,
      OrderStatus.processing: 1,
      OrderStatus.shipped: 2,
      OrderStatus.delivered: 3,
    };

    final currentOrder = statusOrder[order.status] ?? 0;
    final checkOrder = statusOrder[status] ?? 0;

    return checkOrder < currentOrder ||
        (checkOrder == currentOrder && order.status != OrderStatus.cancelled);
  }

  String _getStatusDate(OrderStatus status) {
    // For now, return the order's status update date
    // In a real app, you'd have individual timestamps for each status
    return order.formattedStatusDate;
  }

  String _getStatusTime(OrderStatus status) {
    // For now, return the order's status update time
    // In a real app, you'd have individual timestamps for each status
    return _formatTime(order.statusUpdatedAt);
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class _StatusInfo {
  final OrderStatus status;
  final bool isActive;
  final bool isCompleted;
  final String date;
  final String time;

  _StatusInfo({
    required this.status,
    required this.isActive,
    required this.isCompleted,
    required this.date,
    required this.time,
  });
}

class _TimelineItem extends StatelessWidget {
  final OrderStatus status;
  final bool isActive;
  final bool isCompleted;
  final bool isLast;
  final String date;
  final String time;

  const _TimelineItem({
    required this.status,
    required this.isActive,
    required this.isCompleted,
    required this.isLast,
    required this.date,
    required this.time,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            OrderStatusIcon(
              status: status,
              size: 32,
            ),
            if (!isLast) ...[
              Container(
                width: 2,
                height: 40,
                color: isCompleted || isActive
                    ? _getStatusColor()
                    : Colors.grey[300],
              ),
            ],
          ],
        ),
        const SizedBox(width: 16),

        // Status info
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status.displayName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isActive || isCompleted
                            ? _getStatusColor()
                            : Colors.grey[600],
                      ),
                ),
                const SizedBox(height: 4),
                if (isActive || isCompleted) ...[
                  Text(
                    '$date - $time',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case OrderStatus.pending:
        return const Color(0xFF4044AA);
      case OrderStatus.processing:
        return const Color(0xFF41A954);
      case OrderStatus.shipped:
        return const Color(0xFFE19603);
      case OrderStatus.delivered:
        return const Color(0xFF41AA55);
      case OrderStatus.cancelled:
        return const Color(0xFFFF1F1F);
    }
  }
}

/// Order status summary card
class OrderStatusSummaryCard extends StatelessWidget {
  final WholesalerOrder order;

  const OrderStatusSummaryCard({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'حالة الطلب',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                OrderStatusBadge(
                  status: order.status,
                  fontSize: 14,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                OrderStatusIcon(status: order.status),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.status.displayName,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'آخر تحديث: ${order.formattedStatusDate}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      if (order.statusReason.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          'السبب: ${order.statusReason}',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                    fontStyle: FontStyle.italic,
                                  ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
