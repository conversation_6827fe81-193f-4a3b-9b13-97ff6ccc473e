import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../api/api.dart';
import '../../../services/auth.dart';
import '../models/wholesaler_models.dart';

/// Exception class for wholesaler orders API errors
class WholesalerOrdersApiException implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? details;

  const WholesalerOrdersApiException(
    this.message, {
    this.statusCode,
    this.details,
  });

  @override
  String toString() => 'WholesalerOrdersApiException: $message';
}

/// API service for wholesaler orders management
class WholesalerOrdersApiService {
  static const String _baseEndpoint = '/api/v2/wholesaler-orders';

  /// Handle API errors and convert to appropriate exceptions
  static WholesalerOrdersApiException _handleApiError(DioException e) {
    if (e.response != null) {
      final statusCode = e.response!.statusCode;
      final data = e.response!.data;

      String message = 'حدث خطأ غير متوقع';
      Map<String, dynamic>? details;

      if (data is Map<String, dynamic>) {
        if (data.containsKey('error')) {
          message = data['error'].toString();
        } else if (data.containsKey('message')) {
          message = data['message'].toString();
        } else if (data.containsKey('detail')) {
          message = data['detail'].toString();
        }
        details = data;
      }

      // Handle specific status codes
      switch (statusCode) {
        case 400:
          message = data?['error'] ?? 'طلب غير صحيح';
          break;
        case 401:
          message = 'غير مصرح لك بالوصول';
          break;
        case 403:
          message = 'ليس لديك صلاحية للقيام بهذا الإجراء';
          break;
        case 404:
          message = 'الطلب غير موجود';
          break;
        case 500:
          message = 'خطأ في الخادم';
          break;
      }

      return WholesalerOrdersApiException(
        message,
        statusCode: statusCode,
        details: details,
      );
    }

    // Network or other errors
    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout) {
      return const WholesalerOrdersApiException('انتهت مهلة الاتصال');
    }

    if (e.type == DioExceptionType.connectionError) {
      return const WholesalerOrdersApiException('خطأ في الاتصال بالإنترنت');
    }

    return WholesalerOrdersApiException('خطأ غير متوقع: ${e.message}');
  }

  /// Get list of wholesaler orders with pagination
  static Future<WholesalerOrdersListResponse> getOrders({
    int offset = 0,
    int limit = 10,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw const WholesalerOrdersApiException('رمز المصادقة غير موجود');
      }

      final queryParams = {
        'offset': offset.toString(),
        'limit': limit.toString(),
      };

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await HttpService.instance.get(
        '$_baseEndpoint?$queryString',
      );

      return WholesalerOrdersListResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerOrdersApiException(
        'خطأ غير متوقع: ${e.toString()}',
      );
    }
  }

  /// Accept a pending order
  static Future<WholesalerOrderApiResponse> acceptOrder(int orderId) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw const WholesalerOrdersApiException('رمز المصادقة غير موجود');
      }

      final response = await HttpService.instance.post(
        '$_baseEndpoint/accept/$orderId',
        {},
      );

      return WholesalerOrderApiResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerOrdersApiException(
        'خطأ في قبول الطلب: ${e.toString()}',
      );
    }
  }

  /// Reject a pending or processing order
  static Future<WholesalerOrderApiResponse> rejectOrder(int orderId) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw const WholesalerOrdersApiException('رمز المصادقة غير موجود');
      }

      final response = await HttpService.instance.post(
        '$_baseEndpoint/reject/$orderId',
        {},
      );

      return WholesalerOrderApiResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerOrdersApiException(
        'خطأ في رفض الطلب: ${e.toString()}',
      );
    }
  }

  /// Cancel a pending or processing order
  static Future<WholesalerOrderApiResponse> cancelOrder(int orderId) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw const WholesalerOrdersApiException('رمز المصادقة غير موجود');
      }

      final response = await HttpService.instance.post(
        '$_baseEndpoint/cancel/$orderId',
        {},
      );

      return WholesalerOrderApiResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerOrdersApiException(
        'خطأ في إلغاء الطلب: ${e.toString()}',
      );
    }
  }

  /// Complete a processing order
  static Future<WholesalerOrderApiResponse> completeOrder(
    CompleteOrderRequest request,
  ) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw const WholesalerOrdersApiException('رمز المصادقة غير موجود');
      }

      final response = await HttpService.instance.post(
        '$_baseEndpoint/complete',
        request.toJson(),
      );

      return WholesalerOrderApiResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) rethrow;
      throw WholesalerOrdersApiException(
        'خطأ في إكمال الطلب: ${e.toString()}',
      );
    }
  }
}
