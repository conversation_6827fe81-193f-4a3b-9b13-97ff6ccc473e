import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../../../core/components/app_back_button.dart';
import '../repository/wholesaler_orders_repository.dart';
import '../models/wholesaler_models.dart';

class WholesalerOrderDetailsPage extends StatefulWidget {
  final int orderId;

  const WholesalerOrderDetailsPage({
    super.key,
    required this.orderId,
  });

  @override
  State<WholesalerOrderDetailsPage> createState() =>
      _WholesalerOrderDetailsPageState();
}

class _WholesalerOrderDetailsPageState
    extends State<WholesalerOrderDetailsPage> {
  late WholesalerOrdersRepository _repository;
  WholesalerOrder? _order;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _repository = WholesalerOrdersRepository();
    _loadOrderDetails();
  }

  @override
  void dispose() {
    _repository.dispose();
    super.dispose();
  }

  Future<void> _loadOrderDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await _repository.loadOrders();
      _order = _repository.getOrderById(widget.orderId);

      if (_order == null) {
        _error = 'الطلب غير موجود';
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _acceptOrder() async {
    if (_order == null) return;

    final confirmed = await _showConfirmationDialog(
      title: 'قبول الطلب',
      message: 'هل أنت متأكد من قبول هذا الطلب؟',
      confirmText: 'قبول',
      confirmColor: Colors.green,
    );

    if (confirmed == true) {
      final success = await _repository.acceptOrder(_order!.id);
      if (success) {
        _showSuccessSnackBar('تم قبول الطلب بنجاح');
        _loadOrderDetails();
      } else {
        _showErrorSnackBar(_repository.error ?? 'فشل في قبول الطلب');
      }
    }
  }

  Future<void> _rejectOrder() async {
    if (_order == null) return;

    final confirmed = await _showConfirmationDialog(
      title: 'رفض الطلب',
      message: 'هل أنت متأكد من رفض هذا الطلب؟\nسيتم خصم رسوم من محفظتك.',
      confirmText: 'رفض',
      confirmColor: Colors.red,
    );

    if (confirmed == true) {
      final success = await _repository.rejectOrder(_order!.id);
      if (success) {
        _showSuccessSnackBar('تم رفض الطلب');
        _loadOrderDetails();
      } else {
        _showErrorSnackBar(_repository.error ?? 'فشل في رفض الطلب');
      }
    }
  }

  Future<void> _cancelOrder() async {
    if (_order == null) return;

    final confirmed = await _showConfirmationDialog(
      title: 'إلغاء الطلب',
      message: 'هل أنت متأكد من إلغاء هذا الطلب؟\nسيتم خصم رسوم من محفظتك.',
      confirmText: 'إلغاء',
      confirmColor: Colors.red,
    );

    if (confirmed == true) {
      final success = await _repository.cancelOrder(_order!.id);
      if (success) {
        _showSuccessSnackBar('تم إلغاء الطلب');
        _loadOrderDetails();
      } else {
        _showErrorSnackBar(_repository.error ?? 'فشل في إلغاء الطلب');
      }
    }
  }

  Future<void> _completeOrder() async {
    if (_order == null) return;

    final finalPrice = await _showCompletePriceDialog();
    if (finalPrice != null) {
      final success = await _repository.completeOrder(
        _order!.id,
        finalPrice: finalPrice,
      );
      if (success) {
        _showSuccessSnackBar('تم إكمال الطلب بنجاح');
        _loadOrderDetails();
      } else {
        _showErrorSnackBar(_repository.error ?? 'فشل في إكمال الطلب');
      }
    }
  }

  Future<bool?> _showConfirmationDialog({
    required String title,
    required String message,
    required String confirmText,
    required Color confirmColor,
  }) async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  Future<double?> _showCompletePriceDialog() async {
    final controller = TextEditingController(
      text: _order?.totalPrice.toStringAsFixed(2),
    );

    return showDialog<double>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إكمال الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل السعر النهائي للطلب:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'السعر النهائي',
                suffixText: 'ج.م',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final price = double.tryParse(controller.text);
              Navigator.of(context).pop(price);
            },
            child: const Text('إكمال'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cardColor,
      appBar: AppBar(
        title: Text('تفاصيل الطلب #${widget.orderId}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        leading: const AppBackButton(),
        elevation: 0,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadOrderDetails,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_order == null) {
      return const Center(
        child: Text('الطلب غير موجود'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDefaults.padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOrderHeader(),
          const SizedBox(height: AppDefaults.padding),
          _buildStoreInfo(),
          const SizedBox(height: AppDefaults.padding),
          _buildOrderSummary(),
          const SizedBox(height: AppDefaults.padding),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildOrderHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'طلب #${_order!.id}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _order!.statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _order!.statusColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    _order!.status.displayName,
                    style: TextStyle(
                      color: _order!.statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'تاريخ التحديث: ${_order!.formattedStatusDate}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            if (_order!.deliverAt != null) ...[
              const SizedBox(height: 4),
              Text(
                'موعد التسليم: ${_order!.formattedDeliveryDate}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStoreInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المتجر',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.store, 'اسم المتجر', _order!.store.name),
            const SizedBox(height: 8),
            _buildInfoRow(
                Icons.person, 'صاحب المتجر', _order!.store.owner.fullName),
            const SizedBox(height: 8),
            _buildInfoRow(Icons.phone, 'رقم الهاتف', _order!.store.owner.phone),
            const SizedBox(height: 8),
            _buildInfoRow(
                Icons.location_on, 'العنوان', _order!.store.fullAddress),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrderSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الطلب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            _buildSummaryRow(
                'عدد المنتجات', '${_order!.productsTotalQuantity}'),
            _buildSummaryRow('إجمالي المنتجات',
                '${_order!.productsTotalPrice.toStringAsFixed(2)} ج.م'),
            _buildSummaryRow(
                'الرسوم', '${_order!.fees.toStringAsFixed(2)} ج.م'),
            const Divider(),
            _buildSummaryRow(
              'المبلغ الإجمالي',
              '${_order!.totalPrice.toStringAsFixed(2)} ج.م',
              isTotal: true,
            ),
            if (_order!.finalCompletedPrice > 0) ...[
              const Divider(),
              _buildSummaryRow(
                'السعر النهائي',
                '${_order!.finalCompletedPrice.toStringAsFixed(2)} ج.م',
                isTotal: true,
                color: Colors.green,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value,
      {bool isTotal = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                  color: color,
                ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                  color: color ?? (isTotal ? AppColors.primary : null),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات الطلب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (_order!.canAccept) ...[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _acceptOrder,
                  icon: const Icon(Icons.check),
                  label: const Text('قبول الطلب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 8),
            ],
            if (_order!.canReject) ...[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _rejectOrder,
                  icon: const Icon(Icons.close),
                  label: const Text('رفض الطلب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 8),
            ],
            if (_order!.canCancel) ...[
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _cancelOrder,
                  icon: const Icon(Icons.cancel),
                  label: const Text('إلغاء الطلب'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
              const SizedBox(height: 8),
            ],
            if (_order!.canComplete) ...[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _completeOrder,
                  icon: const Icon(Icons.check_circle),
                  label: const Text('إكمال الطلب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
            if (!_order!.canAccept &&
                !_order!.canReject &&
                !_order!.canCancel &&
                !_order!.canComplete) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'لا توجد إجراءات متاحة لهذا الطلب',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
